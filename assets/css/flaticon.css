@font-face {
  font-family: 'icomoon';
  src:  url('../fonts/icomoonc4db.eot?axc0in');
  src:  url('../fonts/icomoonc4db.eot?axc0in#iefix') format('embedded-opentype'),
    url('../fonts/icomoonc4db.ttf?axc0in') format('truetype'),
    url('../fonts/icomoonc4db.woff?axc0in') format('woff'),
    url('../fonts/icomoonc4db.svg?axc0in#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-1:before {
  content: "\e900";
}
.icon-2:before {
  content: "\e901";
}
.icon-3:before {
  content: "\e902";
}
.icon-4:before {
  content: "\e903";
}
.icon-5:before {
  content: "\e904";
}
.icon-6:before {
  content: "\e905";
}
.icon-7:before {
  content: "\e906";
}
.icon-8:before {
  content: "\e907";
}
.icon-9:before {
  content: "\e908";
}
.icon-10:before {
  content: "\e909";
}
.icon-11:before {
  content: "\e90a";
}
.icon-12:before {
  content: "\e90b";
}
.icon-13:before {
  content: "\e90c";
}
.icon-14:before {
  content: "\e90d";
}
.icon-15:before {
  content: "\e90e";
}
.icon-16:before {
  content: "\e90f";
}
.icon-17:before {
  content: "\e910";
}
.icon-18:before {
  content: "\e911";
}
.icon-19:before {
  content: "\e912";
}
.icon-20:before {
  content: "\e913";
}
.icon-21:before {
  content: "\e914";
}
.icon-22:before {
  content: "\e915";
}
.icon-23:before {
  content: "\e916";
}
.icon-24:before {
  content: "\e917";
}
.icon-25:before {
  content: "\e918";
}
.icon-26:before {
  content: "\e919";
}
.icon-27:before {
  content: "\e91a";
}
.icon-28:before {
  content: "\e91b";
}
.icon-29:before {
  content: "\e91c";
}
.icon-30:before {
  content: "\e91d";
}
.icon-31:before {
  content: "\e91e";
}
.icon-32:before {
  content: "\e91f";
}
.icon-33:before {
  content: "\e920";
}
.icon-34:before {
  content: "\e921";
}
.icon-35:before {
  content: "\e922";
}
.icon-36:before {
  content: "\e923";
}
.icon-37:before {
  content: "\e924";
}
.icon-38:before {
  content: "\e925";
}
.icon-39:before {
  content: "\e926";
}
.icon-40:before {
  content: "\e927";
}
.icon-41:before {
  content: "\e928";
}
.icon-42:before {
  content: "\e929";
}
.icon-43:before {
  content: "\e92a";
}
.icon-44:before {
  content: "\e92b";
}
.icon-45:before {
  content: "\e92c";
}
.icon-46:before {
  content: "\e92d";
}
.icon-47:before {
  content: "\e92e";
}
.icon-48:before {
  content: "\e92f";
}
.icon-49:before {
  content: "\e930";
}
.icon-50:before {
  content: "\e931";
}
.icon-51:before {
  content: "\e932";
}
.icon-52:before {
  content: "\e933";
}
.icon-53:before {
  content: "\e934";
}
.icon-54:before {
  content: "\e935";
}
.icon-55:before {
  content: "\e936";
}
.icon-56:before {
  content: "\e937";
}
.icon-57:before {
  content: "\e938";
}
.icon-58:before {
  content: "\e939";
}
.icon-59:before {
  content: "\e93a";
}
.icon-60:before {
  content: "\e93b";
}